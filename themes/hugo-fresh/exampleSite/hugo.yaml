baseURL: http://something-fresh.org/
languageCode: en-us
title: <PERSON> Fresh Theme

module:
  # uncomment line below for local development of module
  # workspace: hugo.work
  imports:
    path: github.com/StefMa/hugo-fresh


googleAnalytics: # Put in your tracking code without quotes like this: UA-XXX for universal tracking or G-XXX analytics v4 tracking
# Disables warnings
disableKinds:
- taxonomy
markup:
  goldmark:
    renderer:
      unsafe: true # Allows you to write raw html in your md files

params:
  # Open graph allows easy social sharing. If you don't want it you can set it to false or just delete the variable
  openGraph: true
  # Used as meta data; describe your site to make Google Bots happy
  description:
  # Preloader ensures images are loaded before displaying to the user. If you don't want it uncomment to set it to false
  # preloader: false
  navbarlogo:
  # Logo (from static/images/logos/___)
    image: logos/fresh.svg
    link: /
    # Default width/height. Uncomment if you need to change
    # width: 112
    # height: 28
  font:
    name: "Open Sans"
    sizes: [400,600]
  hero:
    # Main hero title
    title: Manage. Deploy.
    # Hero subtitle (optional)
    subtitle: Lorem ipsum sit dolor amet is dummy text used by the typography industry
    # Button text
    buttontext: Get started
    # Where the main hero button links to
    buttonlink: "#"
    # Hero image (from static/images/___)
    image: illustrations/worker.svg
    # Footer logos (from static/images/logos/clients/*.svg)
    # urls are optional
    clientlogos:
    - logo: systek
      url: https://google.com
    - logo: tribe
      url: https://stefma.github.io/hugo-fresh/
    - logo: kromo
      url: https://github.com/StefMa/hugo-fresh
    - logo: infinite
      url: https://hugo-fresh.vercel.app/
    - logo: gutwork
      url: https://bulma.io/
  # Customizable navbar. For a dropdown, add a "sublinks" list.
  navbar:
  - title: Features
    url: /
  - title: Pricing
    url: /
  - title: Dropdown
    sublinks:
    - title: Dropdown item
      url: /
    - title: Dropdown item
      url: /
    - title: Dropdown item
      url: /
  - title: Log in
    url: /
  - title: Sign up
    url: /
    button: true
  sidebar:
    # Logo (from static/images/logos/___.svg)
    logo: fresh-square
    sections:
    - title: User
      icon: user
      links:
      - text: Profile
        url: /
      - text: Account
        url: /
      - text: Settings
        url: /
    - title: Messages
      icon: envelope
      links:
      - text: Inbox
        url: /
      - text: Compose
        url: /
    - title: Images
      icon: image
      links:
      - text: Library
        url: /
      - text: Upload
        url: /
    - title: Settings
      icon: cog
      links:
      - text: User settings
        url: /
      - text: App settings
        url: /
  section1:
    title: Great power comes
    subtitle: with great responsibility
    tiles:
    - title: App builder
      icon: mouse-globe
      text: This is some explanatory text that is on two rows
      url: /
      buttonText: Free trial
    - title: Cloud integration
      icon: laptop-cloud
      text: This is some explanatory text that is on two rows
      url: /
      buttonText: Get started
    - title: Add-ons & plugins
      icon: plug-cloud
      text: This is some explanatory text that is on two rows
      url: /
      buttonText: Get started
  section2:
    title: You're here because you want the best
    subtitle: And we know it
    features:
    - title: Powerful and unified interface
      text: Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin ornare magna eros, eu pellentesque tortor vestibulum ut. Maecenas non massa sem. Etiam finibus odio quis feugiat facilisis.
      # Icon (from static/images/illustrations/icons/___.svg)
      icon: laptop-globe
    - title: Cross-device synchronisation
      text: Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin ornare magna eros, eu pellentesque tortor vestibulum ut. Maecenas non massa sem. Etiam finibus odio quis feugiat facilisis.
      icon: doc-sync
    - title: Nomad system
      text: Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin ornare magna eros, eu pellentesque tortor vestibulum ut. Maecenas non massa sem. Etiam finibus odio quis feugiat facilisis.
      icon: mobile-feed
  section3:
    title: One platform
    subtitle: To rule them all
    image: illustrations/mockups/app-mockup.png
    buttonText: Get started
    buttonLink: "#"
  section4:
    title: Our Clients love us!
    subtitle: Lorem ipsum sit dolor amet is a dummy text used by typography industry
    clients:
    - name: Irma Walters
      quote: Lorem ipsum dolor sit amet, elit deleniti dissentias quo eu, hinc minim appetere te usu, ea case duis scribentur has. Duo te consequat elaboraret, has quando suavitate at.
      job: Accountant
      img: 1
    - name: John Bradley
      quote: Lorem ipsum dolor sit amet, elit deleniti dissentias quo eu, hinc minim appetere te usu, ea case duis scribentur has. Duo te consequat elaboraret, has quando suavitate at.
      job: Financial Analyst
      img: 2
    - name: Gary Blackman
      quote: Lorem ipsum dolor sit amet, elit deleniti dissentias quo eu, hinc minim appetere te usu, ea case duis scribentur has. Duo te consequat elaboraret, has quando suavitate at.
      job: HR Manager
      img: 3
  section5:
    title: Drop us a line or two
    subtitle: We'd love to hear from you
    buttonText: Send Message
  footer:
    # Logo (from static/images/logos/___)
    logo: fresh-white-alt.svg
    # Social Media Title
    socialmediatitle: Follow Us
    # Social media links (GitHub, Twitter, etc.). All are optional.
    socialmedia:
    - link: https://github.com/StefMa
      # Icons are from Font Awesome
      icon: github
    - link: https://dribbble.com/#
      icon: dribbble
    - link: https://facebook.com/#
      icon: facebook
    - link: https://twitter.com/StefMa91
      icon: twitter
    - link: https://bitbucket.org/#
      icon: bitbucket
    bulmalogo: true
    quicklinks:
      column1:
        title: "Product"
        links:
        - text: Discover features
          link: /
        - text: Why choose our product?
          link: /
        - text: Compare features
          link: /
        - text: Our roadmap
          link: /
        - text: AGB
          link: /agb
      column2:
        title: "Docs"
        links:
        - text: Get started
          link: /
        - text: User guides
          link: /
        - text: Admin guide
          link: /
        - text: Developers
          link: /
      column3:
        title: "Blog"
        links:
        - text: Latest news
          link: /blog/first
        - text: Tech articles
          link: /blog/second
