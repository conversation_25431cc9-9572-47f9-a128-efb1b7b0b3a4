server {
    listen 80;
    server_name whitesky.cloud www.whitesky.cloud;

    root /var/www/production;
    index index.html;

    location /submit-form {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }    

    location / {
        try_files $uri $uri/ =404;
    }
}
