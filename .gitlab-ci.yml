image: alpine:latest

stages:
  - build
  - deploy

variables:
  HUGO_ENV: production
  GIT_SUBMODULE_STRATEGY: recursive

build:
  stage: build
  script:
    - apk add hugo
    - hugo --minify
  artifacts:
    paths:
      - public/

.deploy_template: &ssh_defaults
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -p $DEPLOY_PORT $DEPLOY_HOST >> ~/.ssh/known_hosts

deploy_staging:
  stage: deploy
  only:
    - master
  <<: *ssh_defaults
  script:
    - scp -P $DEPLOY_PORT -r public/* $DEPLOY_USER@$DEPLOY_HOST:/var/www/staging/
    - scp -P $DEPLOY_PORT nginx/staging.whitesky.cloud.conf $DEPLOY_USER@$DEPLOY_HOST:/etc/nginx/sites-available/
    - ssh -p $DEPLOY_PORT $DEPLOY_USER@$DEPLOY_HOST 'ln -sf /etc/nginx/sites-available/staging.whitesky.cloud.conf /etc/nginx/sites-enabled/'
    - ssh -p $DEPLOY_PORT $DEPLOY_USER@$DEPLOY_HOST 'nginx -t && systemctl reload nginx'

deploy_production:
  only:
    - master
  stage: deploy
  when: manual
  <<: *ssh_defaults
  script:
    - scp -P $DEPLOY_PORT -r public/* $DEPLOY_USER@$DEPLOY_HOST:/var/www/production/
    - scp -P $DEPLOY_PORT nginx/whitesky.cloud.conf $DEPLOY_USER@$DEPLOY_HOST:/etc/nginx/sites-available/
    - ssh -p $DEPLOY_PORT $DEPLOY_USER@$DEPLOY_HOST 'ln -sf /etc/nginx/sites-available/whitesky.cloud.conf /etc/nginx/sites-enabled/'
    - ssh -p $DEPLOY_PORT $DEPLOY_USER@$DEPLOY_HOST 'nginx -t && systemctl reload nginx'

deploy_formserver:
  only:
    - master
  stage: deploy
  when: manual
  <<: *ssh_defaults
  script:
   - echo "SMTP_SERVER=$SMTP_SERVER" > .env
   - echo "SMTP_PORT=$SMTP_PORT" >> .env
   - echo "SMTP_USERNAME=$SMTP_USERNAME" >> .env
   - echo "SMTP_PASSWORD=$SMTP_PASSWORD" >> .env
   - echo "FROM_EMAIL=$FROM_EMAIL" >> .env
   - echo "TO_EMAIL=$TO_EMAIL" >> .env
   - echo  "FLASK_ENV=$FLASK_ENV" >> .env
   - scp -P $DEPLOY_PORT messages_server/requirements.txt $DEPLOY_USER@$DEPLOY_HOST:/opt/form-backend/
   - scp -P $DEPLOY_PORT messages_server/app.py $DEPLOY_USER@$DEPLOY_HOST:/opt/form-backend/
   - scp -P $DEPLOY_PORT messages_server/form-backend.service $DEPLOY_USER@$DEPLOY_HOST:/etc/systemd/system/
   - scp -P $DEPLOY_PORT .env $DEPLOY_USER@$DEPLOY_HOST:/opt/form-backend/
   - ssh -p $DEPLOY_PORT $DEPLOY_USER@$DEPLOY_HOST 'cd /opt/form-backend && source venv/bin/activate && pip install -r requirements.txt'
   - ssh -p $DEPLOY_PORT $DEPLOY_USER@$DEPLOY_HOST 'systemctl daemon-reexec && systemctl restart form-backend && systemctl enable form-backend'
