#!/usr/bin/env python3
"""
Test script for the daily email summary functionality
"""
import requests
import json
import time

# Server configuration
SERVER_URL = "http://localhost:5000"

def test_form_submission(form_type="contact"):
    """Test form submission"""
    print(f"\n=== Testing {form_type} form submission ===")
    
    # Prepare test data based on form type
    if form_type == "pricing":
        data = {
            'name': 'Test User Pricing',
            'email': '<EMAIL>',
            'subject': 'Quote Request',
            'message': 'QUOTE REQUEST DETAILS:\n\nCompany: Test Company\nVCPUs: 4\nMemory: 8GB\nStorage: 100GB',
            '_bot_detection_data': json.dumps({
                'submissionTime': 5000,
                'hasMouseMovement': True,
                'mouseMoveCount': 10,
                'hasFieldFocus': True,
                'fieldFocusEvents': 3,
                'keyboardEvents': 20,
                'honeypotFilled': False,
                'userAgent': 'Mozilla/5.0 Test Browser',
                'timestamp': int(time.time() * 1000)
            })
        }
    elif form_type == "section5":
        data = {
            'name': 'Test User Section5',
            'email': '<EMAIL>',
            'subject': 'General Inquiry',
            'message': 'This is a test message from section5 form.',
            '_bot_detection_data': json.dumps({
                'submissionTime': 4000,
                'hasMouseMovement': True,
                'mouseMoveCount': 8,
                'hasFieldFocus': True,
                'fieldFocusEvents': 2,
                'keyboardEvents': 15,
                'honeypotFilled': False,
                'userAgent': 'Mozilla/5.0 Test Browser',
                'timestamp': int(time.time() * 1000)
            })
        }
    else:  # contact
        data = {
            'name': 'Test User Contact',
            'email': '<EMAIL>',
            'subject': 'Contact Form Test',
            'message': 'This is a test message from the contact form.',
            '_bot_detection_data': json.dumps({
                'submissionTime': 3000,
                'hasMouseMovement': True,
                'mouseMoveCount': 12,
                'hasFieldFocus': True,
                'fieldFocusEvents': 4,
                'keyboardEvents': 25,
                'honeypotFilled': False,
                'userAgent': 'Mozilla/5.0 Test Browser',
                'timestamp': int(time.time() * 1000)
            })
        }
    
    # Add referrer for section5 form
    headers = {}
    if form_type == "section5":
        headers['Referer'] = 'http://localhost:1313/section5'
    
    try:
        response = requests.post(f"{SERVER_URL}/submit-form", data=data, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def check_health():
    """Check server health and pending submissions"""
    print("\n=== Checking server health ===")
    try:
        response = requests.get(f"{SERVER_URL}/health")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.json().get('pending_submissions', 0)
    except Exception as e:
        print(f"Error: {e}")
        return 0

def test_daily_summary():
    """Test daily summary email"""
    print("\n=== Testing daily summary email ===")
    try:
        response = requests.post(f"{SERVER_URL}/test-daily-summary")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    """Main test function"""
    print("Starting form submission and daily email summary tests...")
    
    # Check initial health
    initial_count = check_health()
    print(f"Initial pending submissions: {initial_count}")
    
    # Test different form types
    success_count = 0
    
    # Test contact form
    if test_form_submission("contact"):
        success_count += 1
    
    # Test pricing form
    if test_form_submission("pricing"):
        success_count += 1
    
    # Test section5 form
    if test_form_submission("section5"):
        success_count += 1
    
    # Check health after submissions
    final_count = check_health()
    print(f"Final pending submissions: {final_count}")
    print(f"New submissions added: {final_count - initial_count}")
    
    # Test daily summary
    if test_daily_summary():
        print("Daily summary test successful!")
    else:
        print("Daily summary test failed!")
    
    # Check health after summary
    after_summary_count = check_health()
    print(f"Pending submissions after summary: {after_summary_count}")
    
    print(f"\n=== Test Results ===")
    print(f"Successful form submissions: {success_count}/3")
    print(f"Submissions stored: {final_count - initial_count}")
    print(f"Submissions cleared after summary: {final_count - after_summary_count}")

if __name__ == "__main__":
    main()
