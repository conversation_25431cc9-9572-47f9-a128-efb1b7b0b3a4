# Contact Form Backend Server

A Flask-based backend server to handle contact form submissions.

## Features

- **Email Forwarding**: Forwards form submissions via SMTP
- **Rate Limiting**: IP-based rate limiting using token bucket algorithm (5 tokens max, 1 token per minute refill)
- **CORS Support**: Configured for Hugo frontend integration
- **Security**: Input validation and sanitization
- **Logging**: Comprehensive logging for monitoring and debugging
- **Health Check**: `/health` endpoint for monitoring

## Setup

1. **Install Dependencies**:
   ```bash
   cd messages_server
   pip install -r requirements.txt
   ```

2. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with SMTP settings
   ```

3. **Required Environment Variables**:
   - `SMTP_USERNAME`: SMTP username
   - `SMTP_PASSWORD`: SMTP password (use app password for Gmail)
   - `TO_EMAIL`: Destination email for form submissions

## Running the Server

### Development
```bash
FLASK_ENV=development python app.py
```

### Production
```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## API Endpoints

### POST /submit-form
Handles contact form submissions.

**Request Body** (form-data):
- `name` (required): Sender's name
- `email` (required): Sender's email
- `subject` (optional): Email subject
- `message` (required): Message content

**Response**:
```json
{
  "success": true,
  "message": "Thank you! Your message has been sent successfully."
}
```

### GET /health
Health check endpoint.

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00"
}
```

## Rate Limiting

- Each IP address gets 5 tokens maximum
- Tokens refill at 1 token per minute
- When rate limited, returns success but doesn't send email
- Prevents spam while maintaining user experience

## SMTP Configuration

### Gmail Setup
1. Enable 2-factor authentication
2. Generate an app password
3. Use app password in `SMTP_PASSWORD`

### Other Providers
Update `SMTP_SERVER` and `SMTP_PORT` accordingly:
- **Outlook**: smtp-mail.outlook.com:587
- **Yahoo**: smtp.mail.yahoo.com:587
- **Custom SMTP**: Your provider's settings

## Deployment

The server is designed to be deployed behind a reverse proxy (nginx, Apache) or on platforms like:
- Heroku
- DigitalOcean App Platform
- AWS Elastic Beanstalk
- Docker containers

## Monitoring

- Logs are written to `contact_form.log` and stdout
- Use the `/health` endpoint for uptime monitoring
- Monitor log files for rate limiting and errors
