#!/usr/bin/env python3
"""
Demo script to generate and save a sample HTML email template
This shows what the daily summary email will look like
"""
import sys
import os
from datetime import datetime

# Add the current directory to Python path to import app
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import generate_daily_summary_html

def create_sample_submissions():
    """Create sample form submissions for demonstration"""
    return [
        {
            'form_type': 'contact',
            'name': '<PERSON>',
            'email': '<EMAIL>',
            'subject': 'General Inquiry',
            'message': 'Hello, I am interested in learning more about your cloud services. Could you please provide more information about your pricing and features?',
            'ip_address': '*************',
            'timestamp': '2024-01-15 14:30:15'
        },
        {
            'form_type': 'pricing',
            'name': '<PERSON>',
            'email': '<EMAIL>',
            'subject': 'Quote Request',
            'message': 'QUOTE REQUEST DETAILS:\n\nCompany: TechCorp Solutions\nPhone: ******-0123\nVCPUs: 8\nMemory: 16GB\nBlock Storage: 500GB\nObject Storage: 1TB\nService Model: Managed\nHardware Quote: Yes\n\nAdditional Information:\nWe need this setup for our development environment. Timeline is flexible but would prefer to start within 2 weeks.',
            'ip_address': '************',
            'timestamp': '2024-01-15 16:45:22'
        },
        {
            'form_type': 'section5',
            'name': 'Mike Johnson',
            'email': '<EMAIL>',
            'subject': 'Partnership Opportunity',
            'message': 'Hi there! I represent a growing startup and we are looking for reliable cloud infrastructure partners. We would love to discuss potential collaboration opportunities.',
            'ip_address': '*************',
            'timestamp': '2024-01-15 18:20:33'
        },
        {
            'form_type': 'contact',
            'name': 'Sarah Wilson',
            'email': '<EMAIL>',
            'subject': 'Technical Support',
            'message': 'We are experiencing some issues with our current cloud setup and need technical assistance. Could someone from your team reach out to us?',
            'ip_address': '***********',
            'timestamp': '2024-01-15 19:15:44'
        },
        {
            'form_type': 'pricing',
            'name': 'David Chen',
            'email': '<EMAIL>',
            'subject': 'Enterprise Quote',
            'message': 'QUOTE REQUEST DETAILS:\n\nCompany: Chen Consulting Group\nPhone: ******-0199\nVCPUs: 16\nMemory: 32GB\nBlock Storage: 1TB\nObject Storage: 5TB\nService Model: Self-Managed\nHardware Quote: No\n\nAdditional Information:\nLooking for a cost-effective solution for our client projects. Need high availability and good performance.',
            'ip_address': '************',
            'timestamp': '2024-01-15 20:05:12'
        }
    ]

def main():
    """Generate and save sample HTML email"""
    print("Generating sample daily summary email...")
    
    # Create sample submissions
    sample_submissions = create_sample_submissions()
    
    # Generate HTML content
    html_content = generate_daily_summary_html(sample_submissions)
    
    # Save to file
    output_file = 'sample_daily_summary_email.html'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✓ Sample email saved to: {output_file}")
    print(f"✓ Total submissions in sample: {len(sample_submissions)}")
    
    # Count by form type
    form_counts = {}
    for submission in sample_submissions:
        form_type = submission['form_type']
        form_counts[form_type] = form_counts.get(form_type, 0) + 1
    
    print("✓ Breakdown by form type:")
    for form_type, count in form_counts.items():
        print(f"  - {form_type.title()}: {count} submissions")
    
    print(f"\nYou can open {output_file} in a web browser to preview the email template.")
    
    # Also generate empty email for comparison
    empty_html = generate_daily_summary_html([])
    empty_file = 'sample_empty_daily_summary.html'
    with open(empty_file, 'w', encoding='utf-8') as f:
        f.write(empty_html)
    
    print(f"✓ Empty email template saved to: {empty_file}")

if __name__ == "__main__":
    main()
